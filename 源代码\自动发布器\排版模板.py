# -*- coding: utf-8 -*-
"""
微信公众号文章排版模板系统

作者: AI助手
日期: 2025-07-28
功能: 提供多种排版风格的HTML和CSS模板
"""

import os
import sys
import re
from datetime import datetime

# 添加配置文件路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '配置文件'))
from 微信发布配置 import 获取排版样式配置

class 排版模板管理器:
    """排版模板管理器"""
    
    def __init__(self):
        self.样式配置 = 获取排版样式配置()
        self.模板缓存 = {}
    
    def 获取基础CSS样式(self, 样式名称='business'):
        """获取基础CSS样式"""
        样式信息 = self.样式配置['样式库'].get(样式名称, self.样式配置['样式库']['business'])
        
        css_template = f"""
        <style>
        .article-container {{
            max-width: 100%;
            margin: 0 auto;
            padding: 20px;
            font-family: {样式信息['字体配置']['正文字体']};
            font-size: {样式信息['字体配置']['正文大小']};
            line-height: {样式信息['字体配置']['行高']};
            color: #333;
            background-color: #fff;
        }}
        
        .article-title {{
            font-family: {样式信息['字体配置']['标题字体']};
            font-size: {样式信息['字体配置']['标题大小']};
            font-weight: bold;
            color: {样式信息['主色调']};
            text-align: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid {样式信息['辅助色']};
        }}
        
        .article-meta {{
            text-align: center;
            color: #666;
            font-size: 12px;
            margin-bottom: 30px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }}
        
        .article-content {{
            text-align: justify;
            word-wrap: break-word;
        }}
        
        .article-content h1, .article-content h2, .article-content h3 {{
            color: {样式信息['主色调']};
            margin-top: 25px;
            margin-bottom: 15px;
            font-weight: bold;
        }}
        
        .article-content h1 {{
            font-size: 20px;
            border-left: 4px solid {样式信息['辅助色']};
            padding-left: 15px;
        }}
        
        .article-content h2 {{
            font-size: 18px;
            border-left: 3px solid {样式信息['辅助色']};
            padding-left: 12px;
        }}
        
        .article-content h3 {{
            font-size: 16px;
            border-left: 2px solid {样式信息['辅助色']};
            padding-left: 10px;
        }}
        
        .article-content p {{
            margin-bottom: 15px;
            text-indent: 2em;
        }}
        
        .article-content blockquote {{
            margin: 20px 0;
            padding: 15px 20px;
            background-color: #f8f9fa;
            border-left: 4px solid {样式信息['辅助色']};
            font-style: italic;
            color: #666;
        }}
        
        .article-content ul, .article-content ol {{
            margin: 15px 0;
            padding-left: 30px;
        }}
        
        .article-content li {{
            margin-bottom: 8px;
        }}
        
        .article-content img {{
            max-width: 100%;
            height: auto;
            display: block;
            margin: 20px auto;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }}
        
        .article-content code {{
            background-color: #f4f4f4;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
        }}
        
        .article-content pre {{
            background-color: #f8f8f8;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            margin: 15px 0;
            border: 1px solid #e1e1e1;
        }}
        
        .article-footer {{
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            text-align: center;
            color: #999;
            font-size: 12px;
        }}
        
        .highlight-box {{
            background: linear-gradient(135deg, {样式信息['辅助色']}22, {样式信息['主色调']}22);
            border: 1px solid {样式信息['辅助色']};
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }}
        
        .emphasis {{
            color: {样式信息['辅助色']};
            font-weight: bold;
        }}
        
        .divider {{
            text-align: center;
            margin: 30px 0;
            color: {样式信息['辅助色']};
        }}
        </style>
        """
        
        return css_template.strip()
    
    def 获取HTML模板(self, 样式名称='business'):
        """获取HTML模板"""
        css_styles = self.获取基础CSS样式(样式名称)
        
        html_template = f"""
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>{{title}}</title>
            {css_styles}
        </head>
        <body>
            <div class="article-container">
                <h1 class="article-title">{{title}}</h1>
                <div class="article-meta">
                    <span>📅 {{publish_date}}</span>
                    {{#author}}<span> | ✍️ {{author}}</span>{{/author}}
                    {{#source}}<span> | 📖 来源：{{source}}</span>{{/source}}
                </div>
                <div class="article-content">
                    {{content}}
                </div>
                <div class="article-footer">
                    <div class="divider">◆ ◇ ◆</div>
                    <p>感谢您的阅读，欢迎分享和关注！</p>
                    {{#original_link}}<p><a href="{{original_link}}">📎 原文链接</a></p>{{/original_link}}
                </div>
            </div>
        </body>
        </html>
        """
        
        return html_template.strip()
    
    def 应用排版样式(self, 内容, 标题, 样式名称='business', **kwargs):
        """应用排版样式到内容"""
        # 获取样式配置
        样式信息 = self.样式配置['样式库'].get(样式名称, self.样式配置['样式库']['business'])

        # 处理内容格式
        处理后内容 = self._处理内容格式(内容, 样式名称)

        # 准备模板变量
        模板变量 = {
            'title': 标题,
            'content': 处理后内容,
            'publish_date': kwargs.get('发布日期', datetime.now().strftime('%Y年%m月%d日')),
            'author': kwargs.get('作者', ''),
            'source': kwargs.get('来源', ''),
            'original_link': kwargs.get('原文链接', '')
        }

        # 获取HTML模板
        html_template = self.获取HTML模板(样式名称)

        # 改进的模板替换逻辑
        结果HTML = html_template

        # 首先处理条件模板
        for key, value in 模板变量.items():
            if value and str(value).strip():
                # 如果有值，保留条件模板内容并替换变量
                pattern = f'{{{{#{key}}}}}(.*?){{{{/{key}}}}}'
                matches = re.findall(pattern, 结果HTML, flags=re.DOTALL)
                for match in matches:
                    # 替换条件模板内的变量
                    replaced_content = match.replace(f'{{{{{key}}}}}', str(value))
                    结果HTML = re.sub(pattern, replaced_content, 结果HTML, count=1, flags=re.DOTALL)
            else:
                # 如果没有值，移除整个条件模板
                结果HTML = re.sub(f'{{{{#{key}}}}}(.*?){{{{/{key}}}}}', '', 结果HTML, flags=re.DOTALL)

        # 然后处理普通变量替换
        for key, value in 模板变量.items():
            if value and str(value).strip():
                结果HTML = 结果HTML.replace(f'{{{{{key}}}}}', str(value))
            else:
                # 移除未替换的变量占位符
                结果HTML = 结果HTML.replace(f'{{{{{key}}}}}', '')

        # 最后清理任何剩余的模板语法
        import re
        # 移除任何剩余的条件模板标记
        结果HTML = re.sub(r'\{\{#[^}]+\}\}', '', 结果HTML)
        结果HTML = re.sub(r'\{\{/[^}]+\}\}', '', 结果HTML)
        # 移除任何剩余的变量占位符
        结果HTML = re.sub(r'\{\{[^}]+\}\}', '', 结果HTML)

        return 结果HTML
    
    def _处理内容格式(self, 内容, 样式名称):
        """处理内容格式"""
        # 移除多余空行
        内容 = re.sub(r'\n\s*\n\s*\n', '\n\n', 内容)
        
        # 处理段落
        段落列表 = 内容.split('\n\n')
        处理后段落 = []
        
        for 段落 in 段落列表:
            段落 = 段落.strip()
            if not 段落:
                continue
                
            # 处理标题
            if 段落.startswith('#'):
                处理后段落.append(self._处理标题(段落))
            # 处理引用
            elif 段落.startswith('>'):
                处理后段落.append(self._处理引用(段落))
            # 处理列表
            elif re.match(r'^[\d\-\*\+]\s', 段落):
                处理后段落.append(self._处理列表(段落))
            # 处理普通段落
            else:
                处理后段落.append(f'<p>{段落}</p>')
        
        return '\n'.join(处理后段落)
    
    def _处理标题(self, 标题文本):
        """处理标题格式"""
        level = 0
        while 标题文本.startswith('#'):
            level += 1
            标题文本 = 标题文本[1:]
        
        标题文本 = 标题文本.strip()
        level = min(level, 3)  # 最多支持h3
        
        return f'<h{level}>{标题文本}</h{level}>'
    
    def _处理引用(self, 引用文本):
        """处理引用格式"""
        引用文本 = 引用文本.lstrip('> ').strip()
        return f'<blockquote>{引用文本}</blockquote>'
    
    def _处理列表(self, 列表文本):
        """处理列表格式"""
        行列表 = 列表文本.split('\n')
        列表项 = []
        
        for 行 in 行列表:
            行 = 行.strip()
            if re.match(r'^[\-\*\+]\s', 行):
                项目内容 = re.sub(r'^[\-\*\+]\s', '', 行)
                列表项.append(f'<li>{项目内容}</li>')
            elif re.match(r'^\d+\.\s', 行):
                项目内容 = re.sub(r'^\d+\.\s', '', 行)
                列表项.append(f'<li>{项目内容}</li>')
        
        if 列表项:
            if re.match(r'^\d+\.', 行列表[0]):
                return f'<ol>{"".join(列表项)}</ol>'
            else:
                return f'<ul>{"".join(列表项)}</ul>'
        
        return 列表文本
    
    def 获取可用样式列表(self):
        """获取可用的样式列表"""
        return list(self.样式配置['样式库'].keys())
    
    def 获取样式信息(self, 样式名称):
        """获取指定样式的详细信息"""
        return self.样式配置['样式库'].get(样式名称)


def 创建排版模板文件():
    """创建排版模板文件到指定目录"""
    模板管理器 = 排版模板管理器()
    模板目录 = os.path.join(os.path.dirname(__file__), 'templates')
    
    # 创建模板目录
    os.makedirs(模板目录, exist_ok=True)
    
    # 为每种样式创建模板文件
    for 样式名称 in 模板管理器.获取可用样式列表():
        # 创建CSS文件
        css_content = 模板管理器.获取基础CSS样式(样式名称)
        css_文件路径 = os.path.join(模板目录, f'{样式名称}_style.css')
        with open(css_文件路径, 'w', encoding='utf-8') as f:
            f.write(css_content)
        
        # 创建HTML模板文件
        html_content = 模板管理器.获取HTML模板(样式名称)
        html_文件路径 = os.path.join(模板目录, f'{样式名称}_template.html')
        with open(html_文件路径, 'w', encoding='utf-8') as f:
            f.write(html_content)
    
    print(f"✅ 排版模板文件已创建到: {模板目录}")


if __name__ == "__main__":
    # 测试排版模板
    模板管理器 = 排版模板管理器()
    
    print("🎨 可用排版样式:")
    for 样式名称 in 模板管理器.获取可用样式列表():
        样式信息 = 模板管理器.获取样式信息(样式名称)
        print(f"  - {样式名称}: {样式信息['名称']} - {样式信息['描述']}")
    
    # 创建模板文件
    创建排版模板文件()
