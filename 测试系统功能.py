#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微信公众号自动发布系统功能测试脚本

作者: AI助手
日期: 2025-07-29
功能: 测试系统各个组件的功能
"""

import os
import sys
from datetime import datetime

def 测试配置文件():
    """测试配置文件导入"""
    print("🔧 测试配置文件导入...")
    
    try:
        # 添加配置文件路径
        sys.path.append(os.path.join(os.path.dirname(__file__), '配置文件'))
        
        from 微信发布配置 import 获取微信API配置, 获取发布控制配置, 获取排版样式配置
        
        print("✅ 微信发布配置导入成功")
        
        # 测试配置获取
        api_config = 获取微信API配置()
        publish_config = 获取发布控制配置()
        style_config = 获取排版样式配置()
        
        print(f"✅ API配置获取成功: {len(api_config)} 项")
        print(f"✅ 发布配置获取成功: {len(publish_config)} 项")
        print(f"✅ 样式配置获取成功: {len(style_config)} 项")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置文件测试失败: {str(e)}")
        return False

def 测试排版模板():
    """测试排版模板系统"""
    print("\n🎨 测试排版模板系统...")
    
    try:
        # 添加自动发布器路径
        sys.path.append(os.path.join(os.path.dirname(__file__), '源代码', '自动发布器'))
        
        from 排版模板 import 排版模板管理器
        
        模板管理器 = 排版模板管理器()
        print("✅ 排版模板管理器初始化成功")
        
        # 测试获取样式列表
        样式列表 = 模板管理器.获取可用样式列表()
        print(f"✅ 可用样式: {', '.join(样式列表)}")
        
        # 测试模板应用
        测试内容 = "# 测试标题\n\n这是一段测试内容。\n\n## 子标题\n\n- 列表项1\n- 列表项2"
        测试标题 = "测试文章"
        
        for 样式 in 样式列表[:2]:  # 只测试前两个样式
            try:
                结果 = 模板管理器.应用排版样式(测试内容, 测试标题, 样式)
                print(f"✅ {样式} 样式应用成功，生成HTML长度: {len(结果)}")
            except Exception as e:
                print(f"❌ {样式} 样式应用失败: {str(e)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 排版模板测试失败: {str(e)}")
        return False

def 测试文章处理器():
    """测试文章处理器"""
    print("\n📝 测试文章处理器...")
    
    try:
        from 文章处理器 import 文章处理器
        
        处理器 = 文章处理器()
        print("✅ 文章处理器初始化成功")
        
        # 测试文章处理
        测试内容 = """
# 投资套利测试文章

这是一篇关于投资套利的测试文章。

## 主要内容

- 套利基本概念
- 风险管理
- 实际案例

投资有风险，入市需谨慎。
        """
        
        处理结果 = 处理器.处理文章内容(
            原始内容=测试内容,
            标题="投资套利测试文章",
            排版样式="business"
        )
        
        print(f"✅ 文章处理成功")
        print(f"   - 标题: {处理结果.get('标题', 'N/A')}")
        print(f"   - 内容长度: {len(处理结果.get('格式化内容', ''))}")
        print(f"   - 摘要: {处理结果.get('摘要', 'N/A')[:50]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ 文章处理器测试失败: {str(e)}")
        return False

def 测试AI配图系统():
    """测试AI配图系统"""
    print("\n🎨 测试AI配图系统...")
    
    try:
        from AI配图系统 import AI配图生成器
        
        配图生成器 = AI配图生成器()
        print("✅ AI配图生成器初始化成功")
        
        # 测试本地配图生成（最安全的方式）
        try:
            本地图片路径 = 配图生成器._创建本地配图("投资套利测试")
            if 本地图片路径 and os.path.exists(本地图片路径):
                print(f"✅ 本地配图生成成功: {os.path.basename(本地图片路径)}")
            else:
                print("⚠️  本地配图生成失败")
        except Exception as e:
            print(f"⚠️  本地配图测试失败: {str(e)}")
        
        return True
        
    except Exception as e:
        print(f"❌ AI配图系统测试失败: {str(e)}")
        return False

def 测试微信API():
    """测试微信API连接"""
    print("\n📱 测试微信API...")
    
    try:
        from 微信API import 微信公众号API
        
        微信api = 微信公众号API()
        print("✅ 微信API初始化成功")
        
        # 验证配置
        配置有效, 错误列表 = 微信api.验证配置()
        if 配置有效:
            print("✅ 微信API配置验证通过")
        else:
            print("⚠️  微信API配置验证失败:")
            for error in 错误列表:
                print(f"   - {error}")
        
        return True
        
    except Exception as e:
        print(f"❌ 微信API测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🚀 微信公众号自动发布系统功能测试")
    print("=" * 60)
    print(f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    测试结果 = []
    
    # 执行各项测试
    测试结果.append(("配置文件", 测试配置文件()))
    测试结果.append(("排版模板", 测试排版模板()))
    测试结果.append(("文章处理器", 测试文章处理器()))
    测试结果.append(("AI配图系统", 测试AI配图系统()))
    测试结果.append(("微信API", 测试微信API()))
    
    # 输出测试总结
    print("\n" + "=" * 60)
    print("📊 测试结果总结")
    print("=" * 60)
    
    成功数 = 0
    总数 = len(测试结果)
    
    for 测试名称, 结果 in 测试结果:
        状态 = "✅ 通过" if 结果 else "❌ 失败"
        print(f"{测试名称:12} : {状态}")
        if 结果:
            成功数 += 1
    
    print("-" * 60)
    print(f"总体结果: {成功数}/{总数} 项测试通过")
    
    if 成功数 == 总数:
        print("🎉 所有测试通过！系统功能正常")
    elif 成功数 >= 总数 * 0.8:
        print("⚠️  大部分测试通过，系统基本可用")
    else:
        print("❌ 多项测试失败，需要检查系统配置")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
