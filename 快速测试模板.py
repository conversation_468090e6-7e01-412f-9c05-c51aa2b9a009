#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试模板替换

作者: AI助手
日期: 2025-07-29
功能: 快速测试模板变量替换
"""

import os
import sys
from datetime import datetime

# 添加路径
sys.path.append(os.path.join(os.path.dirname(__file__), '源代码', '自动发布器'))
sys.path.append(os.path.join(os.path.dirname(__file__), '配置文件'))

def main():
    """主函数"""
    print("🚀 快速测试模板替换")
    
    try:
        from 排版模板 import 排版模板管理器
        
        模板管理器 = 排版模板管理器()
        print("✅ 模板管理器初始化成功")
        
        # 测试数据
        测试标题 = "投资套利策略测试"
        测试内容 = "这是一篇测试文章内容。"
        
        # 应用模板
        结果 = 模板管理器.应用排版样式(
            内容=测试内容,
            标题=测试标题,
            样式名称='business',
            作者='测试作者',
            来源='测试来源',
            发布日期=datetime.now().strftime('%Y年%m月%d日')
        )
        
        print(f"✅ 模板应用成功，结果长度: {len(结果)}")
        
        # 检查是否还有未替换的变量
        import re
        未替换变量 = re.findall(r'\{[a-zA-Z_][a-zA-Z0-9_]*\}', 结果)
        
        if 未替换变量:
            print(f"❌ 发现未替换的变量: {set(未替换变量)}")
        else:
            print("✅ 所有变量都已正确替换")
        
        # 保存结果
        with open('快速测试结果.html', 'w', encoding='utf-8') as f:
            f.write(结果)
        print("💾 结果已保存到: 快速测试结果.html")
        
        # 显示部分结果
        print("\n📋 结果预览:")
        print("=" * 50)
        print(结果[:500] + "..." if len(结果) > 500 else 结果)
        print("=" * 50)
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
