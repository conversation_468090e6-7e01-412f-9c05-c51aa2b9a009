<!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>{title}</title>
            <style>
        .article-container {
            max-width: 100%;
            margin: 0 auto;
            padding: 20px;
            font-family: PingFang SC, Microsoft YaHei, sans-serif;
            font-size: 15px;
            line-height: 1.7;
            color: #333;
            background-color: #fff;
        }
        
        .article-title {
            font-family: PingFang SC, Microsoft YaHei, sans-serif;
            font-size: 19px;
            font-weight: bold;
            color: #1A1A1A;
            text-align: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #00D4FF;
        }
        
        .article-meta {
            text-align: center;
            color: #666;
            font-size: 12px;
            margin-bottom: 30px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        
        .article-content {
            text-align: justify;
            word-wrap: break-word;
        }
        
        .article-content h1, .article-content h2, .article-content h3 {
            color: #1A1A1A;
            margin-top: 25px;
            margin-bottom: 15px;
            font-weight: bold;
        }
        
        .article-content h1 {
            font-size: 20px;
            border-left: 4px solid #00D4FF;
            padding-left: 15px;
        }
        
        .article-content h2 {
            font-size: 18px;
            border-left: 3px solid #00D4FF;
            padding-left: 12px;
        }
        
        .article-content h3 {
            font-size: 16px;
            border-left: 2px solid #00D4FF;
            padding-left: 10px;
        }
        
        .article-content p {
            margin-bottom: 15px;
            text-indent: 2em;
        }
        
        .article-content blockquote {
            margin: 20px 0;
            padding: 15px 20px;
            background-color: #f8f9fa;
            border-left: 4px solid #00D4FF;
            font-style: italic;
            color: #666;
        }
        
        .article-content ul, .article-content ol {
            margin: 15px 0;
            padding-left: 30px;
        }
        
        .article-content li {
            margin-bottom: 8px;
        }
        
        .article-content img {
            max-width: 100%;
            height: auto;
            display: block;
            margin: 20px auto;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .article-content code {
            background-color: #f4f4f4;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
        }
        
        .article-content pre {
            background-color: #f8f8f8;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            margin: 15px 0;
            border: 1px solid #e1e1e1;
        }
        
        .article-footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            text-align: center;
            color: #999;
            font-size: 12px;
        }
        
        .highlight-box {
            background: linear-gradient(135deg, #00D4FF22, #1A1A1A22);
            border: 1px solid #00D4FF;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .emphasis {
            color: #00D4FF;
            font-weight: bold;
        }
        
        .divider {
            text-align: center;
            margin: 30px 0;
            color: #00D4FF;
        }
        </style>
        </head>
        <body>
            <div class="article-container">
                <h1 class="article-title">{title}</h1>
                <div class="article-meta">
                    <span>📅 {publish_date}</span>
                    {#author}<span> | ✍️ {author}</span>{/author}
                    {#source}<span> | 📖 来源：{source}</span>{/source}
                </div>
                <div class="article-content">
                    {content}
                </div>
                <div class="article-footer">
                    <div class="divider">◆ ◇ ◆</div>
                    <p>感谢您的阅读，欢迎分享和关注！</p>
                    {#original_link}<p><a href="{original_link}">📎 原文链接</a></p>{/original_link}
                </div>
            </div>
        </body>
        </html>