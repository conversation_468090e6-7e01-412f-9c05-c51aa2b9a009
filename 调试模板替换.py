#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试模板替换问题

作者: AI助手
日期: 2025-07-29
功能: 调试模板变量替换逻辑
"""

import os
import sys
import re
from datetime import datetime

# 添加路径
sys.path.append(os.path.join(os.path.dirname(__file__), '源代码', '自动发布器'))
sys.path.append(os.path.join(os.path.dirname(__file__), '配置文件'))

def 测试模板替换():
    """测试模板替换逻辑"""
    print("🔍 调试模板替换问题...")
    
    try:
        from 排版模板 import 排版模板管理器
        
        模板管理器 = 排版模板管理器()
        
        # 测试数据
        测试标题 = "投资套利策略测试"
        测试内容 = """
# 投资套利策略测试

这是一篇测试文章。

## 主要内容

- 套利基本概念
- 风险管理
- 实际案例

投资有风险，入市需谨慎。
        """
        
        测试作者 = "测试作者"
        测试来源 = "测试来源"
        测试日期 = datetime.now().strftime('%Y年%m月%d日')
        
        print(f"📋 测试参数:")
        print(f"   标题: {测试标题}")
        print(f"   内容长度: {len(测试内容)}")
        print(f"   作者: {测试作者}")
        print(f"   来源: {测试来源}")
        print(f"   日期: {测试日期}")
        
        # 获取原始模板
        原始模板 = 模板管理器.获取HTML模板('business')
        print(f"\n📄 原始模板长度: {len(原始模板)}")
        
        # 检查原始模板中的变量
        变量列表 = re.findall(r'\{[^}]+\}', 原始模板)
        print(f"🔍 原始模板中的变量: {set(变量列表)}")
        
        # 应用模板
        结果 = 模板管理器.应用排版样式(
            内容=测试内容,
            标题=测试标题,
            样式名称='business',
            作者=测试作者,
            来源=测试来源,
            发布日期=测试日期
        )
        
        print(f"\n📄 处理后模板长度: {len(结果)}")
        
        # 检查处理后的变量
        剩余变量 = re.findall(r'\{[^}]+\}', 结果)
        if 剩余变量:
            print(f"❌ 发现未替换的变量: {set(剩余变量)}")
            
            # 显示未替换变量的上下文
            for 变量 in set(剩余变量):
                位置 = 结果.find(变量)
                if 位置 != -1:
                    开始 = max(0, 位置 - 50)
                    结束 = min(len(结果), 位置 + len(变量) + 50)
                    上下文 = 结果[开始:结束]
                    print(f"   {变量} 的上下文: ...{上下文}...")
        else:
            print("✅ 所有变量都已正确替换")
        
        # 保存结果用于检查
        with open('调试模板结果.html', 'w', encoding='utf-8') as f:
            f.write(结果)
        print(f"💾 结果已保存到: 调试模板结果.html")
        
        return len(剩余变量) == 0
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def 手动测试替换():
    """手动测试替换逻辑"""
    print("\n🔧 手动测试替换逻辑...")
    
    # 简单的测试模板
    测试模板 = """
    <h1>{title}</h1>
    <div>
        <span>📅 {publish_date}</span>
        {#author}<span> | ✍️ {author}</span>{/author}
        {#source}<span> | 📖 来源：{source}</span>{/source}
    </div>
    <div>{content}</div>
    """
    
    # 测试变量
    变量字典 = {
        'title': '测试标题',
        'content': '测试内容',
        'publish_date': '2025年07月29日',
        'author': '测试作者',
        'source': '测试来源'
    }
    
    print("📋 原始模板:")
    print(测试模板)
    
    结果 = 测试模板
    
    # 处理条件模板
    for key, value in 变量字典.items():
        if value and str(value).strip():
            # 处理条件模板
            pattern = f'{{{{#{key}}}}}(.*?){{{{/{key}}}}}'
            matches = re.findall(pattern, 结果, flags=re.DOTALL)
            for match in matches:
                replaced_content = match.replace(f'{{{{{key}}}}}', str(value))
                结果 = re.sub(pattern, replaced_content, 结果, count=1, flags=re.DOTALL)
        else:
            # 移除空的条件模板
            结果 = re.sub(f'{{{{#{key}}}}}(.*?){{{{/{key}}}}}', '', 结果, flags=re.DOTALL)
    
    # 处理普通变量
    for key, value in 变量字典.items():
        if value and str(value).strip():
            结果 = 结果.replace(f'{{{{{key}}}}}', str(value))
        else:
            结果 = 结果.replace(f'{{{{{key}}}}}', '')
    
    print("\n📋 处理后结果:")
    print(结果)
    
    # 检查剩余变量
    剩余变量 = re.findall(r'\{[^}]+\}', 结果)
    if 剩余变量:
        print(f"❌ 剩余变量: {剩余变量}")
        return False
    else:
        print("✅ 手动测试成功，所有变量都已替换")
        return True

def main():
    """主函数"""
    print("🚀 模板替换调试工具")
    print("=" * 60)
    
    # 手动测试
    手动成功 = 手动测试替换()
    
    print("\n" + "=" * 60)
    
    # 系统测试
    系统成功 = 测试模板替换()
    
    print("\n" + "=" * 60)
    print("📊 调试结果总结")
    print("=" * 60)
    print(f"手动测试: {'✅ 成功' if 手动成功 else '❌ 失败'}")
    print(f"系统测试: {'✅ 成功' if 系统成功 else '❌ 失败'}")
    
    if not 系统成功:
        print("\n💡 建议检查:")
        print("1. 模板文件是否正确加载")
        print("2. 变量名称是否匹配")
        print("3. 替换逻辑是否正确执行")
        print("4. 条件模板语法是否正确")

if __name__ == "__main__":
    main()
