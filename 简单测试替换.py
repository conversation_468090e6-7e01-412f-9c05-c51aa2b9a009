#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试模板替换

作者: AI助手
日期: 2025-07-29
功能: 简单测试模板变量替换逻辑
"""

import re

def 测试替换逻辑():
    """测试替换逻辑"""
    print("🔧 测试模板替换逻辑")
    
    # 简单的测试模板
    模板 = """
    <h1>{title}</h1>
    <div>
        <span>📅 {publish_date}</span>
        {#author}<span> | ✍️ {author}</span>{/author}
        {#source}<span> | 📖 来源：{source}</span>{/source}
    </div>
    <div>{content}</div>
    {#original_link}<p><a href="{original_link}">📎 原文链接</a></p>{/original_link}
    """
    
    # 测试变量
    变量字典 = {
        'title': '测试标题',
        'content': '测试内容',
        'publish_date': '2025年07月29日',
        'author': '测试作者',
        'source': '测试来源',
        'original_link': ''  # 空值测试
    }
    
    print("📋 原始模板:")
    print(模板)
    print("\n📋 变量字典:")
    for k, v in 变量字典.items():
        print(f"  {k}: '{v}'")
    
    结果 = 模板
    
    print("\n🔄 开始处理条件模板...")
    # 处理条件模板
    for key, value in 变量字典.items():
        条件模板模式 = f'{{{{#{key}}}}}(.*?){{{{/{key}}}}}'
        print(f"  处理条件模板: {key} = '{value}'")
        
        if value and str(value).strip():
            # 如果有值，保留条件模板内容并替换其中的变量
            def 替换条件模板(match):
                模板内容 = match.group(1)
                print(f"    找到条件模板内容: '{模板内容}'")
                替换后 = 模板内容.replace(f'{{{{{key}}}}}', str(value))
                print(f"    替换后: '{替换后}'")
                return 替换后
            
            结果 = re.sub(条件模板模式, 替换条件模板, 结果, flags=re.DOTALL)
        else:
            # 如果没有值，移除整个条件模板
            print(f"    移除空条件模板: {key}")
            结果 = re.sub(条件模板模式, '', 结果, flags=re.DOTALL)
    
    print("\n🔄 开始处理普通变量...")
    # 处理普通变量
    for key, value in 变量字典.items():
        变量模式 = f'{{{{{key}}}}}'
        print(f"  处理变量: {变量模式} = '{value}'")
        
        if value and str(value).strip():
            结果 = 结果.replace(变量模式, str(value))
            print(f"    已替换")
        else:
            结果 = 结果.replace(变量模式, '')
            print(f"    已移除空变量")
    
    print("\n📋 最终结果:")
    print(结果)
    
    # 检查剩余变量
    剩余变量 = re.findall(r'\{[a-zA-Z_][a-zA-Z0-9_]*\}', 结果)
    if 剩余变量:
        print(f"\n❌ 剩余变量: {剩余变量}")
        return False
    else:
        print(f"\n✅ 所有变量都已替换")
        return True

def main():
    """主函数"""
    成功 = 测试替换逻辑()
    print(f"\n📊 测试结果: {'✅ 成功' if 成功 else '❌ 失败'}")

if __name__ == "__main__":
    main()
