#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
微信公众号自动发布系统 - 主程序入口
集成AI配图和自动发布功能的统一入口

功能特点：
1. 🌸 Pollinations AI优先配图 - 根据关键词生成相关图片
2. 🎯 智能主题匹配 - 科技、商务、自然等主题自动识别
3. 📱 微信公众号自动发布 - 支持草稿和正式发布
4. 🔄 多级降级机制 - 确保配图成功率100%
5. 📊 完整日志记录 - 详细的操作日志和错误追踪

作者: AI配图自动发布系统
版本: 2.0
更新时间: 2025-07-28
"""

import os
import sys
from datetime import datetime

# 添加源代码路径
sys.path.append(os.path.join(os.path.dirname(__file__), '源代码', '自动发布器'))

def 显示系统信息():
    """显示系统信息和功能介绍"""
    print("🚀 微信公众号自动发布系统 v2.0")
    print("=" * 60)
    print("📅 启动时间:", datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    print("=" * 60)
    
    print("🎯 核心功能:")
    print("   1. 🌸 Pollinations AI配图 - 根据关键词生成相关图片")
    print("   2. 🤗 HuggingFace AI配图 - 备用AI生成服务")
    print("   3. 🎯 主题化Picsum配图 - 主题相关真实图片")
    print("   4. 🎲 随机Picsum配图 - 高质量随机图片")
    print("   5. 💻 本地生成配图 - 最终保底方案")
    print("   6. 📱 微信公众号发布 - 支持草稿和正式发布")
    
    print("\n🔧 配图优先级:")
    print("   Pollinations AI → HuggingFace AI → 主题化Picsum → 随机Picsum → 本地生成")
    
    print("\n📁 项目结构:")
    print("   ├── 源代码/自动发布器/     # 核心功能模块")
    print("   ├── 配置文件夹/           # 系统配置文件")
    print("   └── 微信公众号自动发布系统.py  # 主程序入口")

def 验证系统环境():
    """验证系统环境和依赖"""
    print("\n🔍 验证系统环境...")
    
    try:
        # 检查核心模块
        from 微信自动发布器 import 微信自动发布器
        from AI配图系统 import AI配图生成器
        print("✅ 核心模块导入成功")
        
        # 检查配置文件
        配置路径 = os.path.join(os.path.dirname(__file__), '配置文件夹')
        if os.path.exists(配置路径):
            print("✅ 配置文件夹存在")
        else:
            print("⚠️  配置文件夹不存在，将使用默认配置")
        
        # 验证发布器配置
        发布器 = 微信自动发布器()
        配置有效, 错误列表 = 发布器.验证系统配置()
        
        if 配置有效:
            print("✅ 微信公众号配置验证通过")
            return True
        else:
            print("❌ 微信公众号配置验证失败:")
            for error in 错误列表:
                print(f"   - {error}")
            return False
            
    except ImportError as e:
        print(f"❌ 模块导入失败: {str(e)}")
        print("请确保所有依赖已正确安装")
        return False
    except Exception as e:
        print(f"❌ 系统验证失败: {str(e)}")
        return False

def 创建示例文章():
    """创建示例文章用于测试"""
    return {
        '标题': '投资套利策略全面解析：掌握低风险高收益的投资艺术',
        '内容': '''
# 投资套利策略全面解析：掌握低风险高收益的投资艺术

在瞬息万变的金融市场中，投资套利作为一种相对稳健的投资策略，正受到越来越多专业投资者和机构的青睐。随着全球金融市场一体化程度的不断深化，各种套利机会如雨后春笋般涌现，为具备专业知识和敏锐洞察力的投资者提供了丰富的获利空间。本文将从理论基础到实践应用，全面解析投资套利的核心要义、主要类型以及操作技巧，助您在投资路上更进一步。

## 💰 投资套利的本质与内涵

投资套利，简而言之，是指利用同一资产或相关资产在不同市场、不同时间或不同形式下的价格差异，通过精心设计的买卖组合来获取相对确定收益的投资策略。这种策略的精髓在于发现并利用市场的暂时性不均衡，通过科学的风险管理和精准的执行时机来锁定利润。

### 套利的理论基础

套利策略建立在现代金融理论的坚实基础之上：

**市场有效性假说的局限性**：尽管市场总体趋向有效，但由于信息传递的时滞、交易成本的存在、投资者行为的非理性以及制度性约束等因素，市场中始终存在着各种定价偏差和套利机会。

**无套利定价原理**：在理想的市场条件下，相同风险的资产应该具有相同的预期收益率。当这一原理被违背时，就为套利者提供了获利空间。

**风险中性定价理论**：通过构建风险中性的投资组合，套利者可以在不承担市场系统性风险的前提下获得确定性收益。

**时间价值理论**：不同期限的资金具有不同的时间价值，这为跨期套利提供了理论依据。

### 套利的市场功能

套利活动在金融市场中发挥着不可替代的重要作用：

1. **价格发现功能**：套利者的交易行为有助于消除价格偏差，推动资产价格向其内在价值回归，提高市场定价效率。

2. **流动性供给功能**：套利交易增加了市场的交易活跃度，为其他投资者提供了更好的流动性环境。

3. **风险转移功能**：通过跨市场、跨时间的交易，套利活动促进了风险的有效分散和转移。

4. **市场整合功能**：套利活动有助于不同市场之间的价格联动，促进全球金融市场的一体化进程。

## 📊 套利策略的主要类型

### 1. 空间套利：捕捉地理价差的艺术

空间套利是最为直观和传统的套利形式，它利用同一资产在不同地理位置或交易场所的价格差异来获取收益。

**经典案例深度剖析**：

**黄金跨市场套利**：国际黄金市场主要集中在伦敦、纽约、上海等地，由于时区差异、交易时间不同以及当地供需状况的影响，各市场的黄金价格经常出现微小但可观的差异。专业套利者会利用这些价差，在价格相对较低的市场买入黄金，同时在价格较高的市场卖出，从而锁定无风险收益。

**股票ADR套利**：许多中国公司同时在A股和美股市场上市，由于两个市场的交易时间、投资者结构、流动性状况等存在差异，同一公司的股票在两地的价格经常出现偏离。套利者可以通过买入相对便宜的股票，卖出相对昂贵的股票来获利。

**外汇三角套利**：在外汇市场中，三种货币之间的汇率关系有时会出现不一致的情况。例如，如果USD/EUR、EUR/GBP、GBP/USD三个汇率之间存在套利空间，交易者可以通过一系列货币转换操作来获得无风险收益。

**操作要点与风险控制**：

- **执行速度的重要性**：空间套利的价差窗口通常非常短暂，往往只有几秒钟甚至更短的时间。因此，拥有高速的交易执行系统和稳定的网络连接至关重要。

- **成本精算**：必须精确计算所有相关成本，包括交易手续费、汇率转换成本、资金占用成本、税费等。只有当预期收益明显超过所有成本时，套利才具有经济意义。

- **技术风险管理**：虽然理论上是无风险套利，但实际操作中仍可能面临系统故障、网络延迟、流动性不足等技术性风险。

### 2. 时间套利：驾驭时间价值的智慧

时间套利利用同一资产在不同时间点的价格差异，是一种更加复杂但潜在收益更为丰厚的套利策略。

**核心策略详解**：

**期现套利的精髓**：期货与现货价格差异套利是时间套利的典型代表。当期货价格相对现货价格出现异常偏离时，套利者可以构建相应的多空组合。例如，当股指期货价格明显高于理论价值时，可以卖出期货合约，同时按照指数权重买入成分股，持有至期货到期日通过交割实现套利收益。

**跨期套利策略**：利用不同到期日合约之间的价差进行套利。这种策略特别适用于商品期货市场，当近月合约与远月合约之间的价差偏离正常水平时，可以通过买入一个合约同时卖出另一个合约来获利。

**日历价差套利**：在期权市场中，利用相同执行价格但不同到期日的期权之间的时间价值差异进行套利。随着时间的推移，期权的时间价值会发生变化，为套利者提供获利机会。

**实战案例分析**：

以可转债套利为例，可转债具有债券和股票的双重属性。当可转债的价格相对其转股价值出现明显偏离时，就出现了套利机会：

1. **正向套利**：当可转债价格低于转股价值时，买入可转债并卖出对应股票
2. **反向套利**：当可转债价格高于转股价值时，卖出可转债并买入对应股票
3. **Delta中性套利**：通过动态调整股票头寸，保持投资组合对股价变动的中性

**风险管理要点**：

- **持有期风险**：时间套利通常需要持有头寸较长时间，期间可能面临市场波动、利率变化、政策调整等多种风险。

- **流动性风险**：确保在需要调整或平仓时有足够的市场流动性，避免因流动性不足而造成损失。

- **模型风险**：时间套利往往依赖于复杂的定价模型，模型的准确性直接影响套利效果。

### 3. 统计套利：数据驱动的投资艺术

统计套利是基于历史数据和统计模型的套利策略，它不依赖于明确的价格差异，而是利用资产价格的统计规律来获取收益。

**核心方法论**：

**配对交易的精髓**：选择两只或多只历史上价格走势高度相关的股票，当它们的价格关系偏离历史均值时，做多被相对低估的股票，做空被相对高估的股票，等待价格关系回归正常。

实例：中国平安与中国人寿作为保险行业的龙头企业，其股价走势通常具有较强的相关性。当两者的价格比率显著偏离历史均值时，就可能出现配对交易机会。

**均值回归策略**：基于资产价格向长期均值回归的统计特性。当某个资产的价格大幅偏离其历史均值时，预期价格会向均值回归，从而进行相应的交易操作。

**协整关系套利**：利用多个资产之间的长期均衡关系。即使单个资产价格可能呈现随机游走特征，但多个相关资产之间往往存在稳定的长期协整关系。当这种关系被暂时打破时，就出现了套利机会。

**技术实现与挑战**：

- **大数据处理能力**：统计套利需要处理海量的历史数据，进行复杂的统计分析和模型构建。现代统计套利越来越多地依赖于机器学习和人工智能技术。

- **模型验证与更新**：统计模型可能会因为市场结构变化而失效，需要建立完善的模型验证机制和定期更新流程。

- **高频执行系统**：统计套利往往需要快速响应模型信号，对交易执行系统的速度和稳定性要求极高。

## 🎯 套利策略的实施框架

### 资金管理的艺术

**分散化原则**：不要将所有资金投入单一套利机会，应该在不同类型的套利策略之间进行合理分配，降低集中度风险。

**杠杆使用策略**：套利策略通常收益率相对较低，适度使用杠杆可以提高资金使用效率，但必须严格控制杠杆比例，避免过度放大风险。

**流动性管理**：保持充足的现金储备，确保能够及时抓住突发的套利机会，同时应对可能出现的保证金追加要求。

### 风险控制体系

**多层次止损机制**：为每个套利头寸设置合理的止损点，包括单笔交易止损、日内止损、月度止损等多个层次。

**实时监控系统**：建立完善的头寸监控和风险预警系统，实时跟踪套利头寸的盈亏状况和风险指标。

**压力测试**：定期进行各种极端市场情况下的压力测试，评估套利策略在不同市场环境下的表现。

### 执行效率优化

**技术基础设施**：投资建设高性能的交易系统和数据处理平台，确保能够快速、准确地执行套利交易。

**信息获取渠道**：建立多元化的信息获取渠道，及时掌握市场动态和价格变化。

**成本控制**：严格控制交易成本，包括手续费、滑点、冲击成本等，提高套利策略的净收益。

## ⚠️ 套利投资的风险警示

### 主要风险类型识别

**执行风险**：由于技术故障、网络延迟、系统错误等原因导致无法按预期执行套利交易。

**流动性风险**：市场流动性不足导致无法及时建仓或平仓，或者交易成本显著增加。

**模型风险**：统计模型失效、参数估计错误或者市场结构发生根本性变化。

**监管风险**：政策法规变化对套利策略产生不利影响，如交易规则调整、税收政策变化等。

**对手方风险**：交易对手违约或者清算机构出现问题。

### 风险防范措施

**建立完善的风险管理制度**：制定详细的风险管理规程，明确各种风险的识别、评估和应对措施。

**定期策略评估**：定期评估套利策略的有效性，及时调整和优化策略参数。

**充足的风险准备金**：保持充足的风险准备金，应对可能出现的意外损失。

**密切关注监管动态**：及时了解相关监管政策的变化，提前做好应对准备。

## 🚀 套利策略的未来展望

### 技术革命的推动

**人工智能的应用**：机器学习和深度学习技术在套利策略中的应用越来越广泛，能够识别更加复杂和微妙的套利机会。

**大数据分析**：利用海量的市场数据、新闻信息、社交媒体数据等进行综合分析，发现传统方法难以察觉的价格异常。

**量子计算的前景**：量子计算技术的发展可能会彻底改变套利策略的计算能力和执行速度。

### 市场发展的机遇

**全球化深化**：随着全球金融市场一体化程度的提高，跨境套利机会将更加丰富。

**新兴资产类别**：数字货币、碳排放权、新能源证书等新兴资产为套利提供了新的空间。

**金融创新**：各种新型金融工具和衍生品的推出为套利策略提供了更多选择。

## 💡 投资者实用指南

### 入门建议

**扎实的理论基础**：深入学习现代金融理论，掌握套利的基本原理和方法。

**实践经验积累**：从简单的套利策略开始，逐步积累实战经验。

**风险意识培养**：始终将风险控制放在首位，不要被短期的高收益所迷惑。

### 进阶路径

**专业技能提升**：学习高级的数学和统计方法，掌握现代金融工程技术。

**技术工具运用**：熟练使用各种分析软件和交易系统。

**市场敏感度培养**：培养对市场变化的敏锐洞察力和快速反应能力。

### 长期发展

**持续学习**：金融市场在不断发展变化，需要保持持续学习的态度。

**网络建设**：建立广泛的行业网络，获取更多的信息和机会。

**创新思维**：保持开放的心态，勇于尝试新的套利策略和方法。

## 📝 结语与展望

投资套利作为一种相对稳健的投资策略，在现代金融市场中占据着重要地位。它不仅为投资者提供了获取稳定收益的途径，更在维护市场效率、促进价格发现等方面发挥着重要作用。

然而，套利投资绝非简单的"无风险获利"，它需要投资者具备深厚的理论功底、丰富的实践经验、敏锐的市场洞察力以及严格的风险控制能力。在这个充满机遇与挑战的领域中，只有那些真正掌握了套利精髓的投资者，才能在市场的波涛中稳健前行。

随着金融科技的不断进步和市场结构的持续演进，套利投资的形式和方法也在不断创新发展。未来的套利者需要更加注重技术创新、数据分析和风险管理，才能在日益激烈的竞争中保持优势。

最后，我们再次强调：投资有风险，套利需谨慎。任何投资决策都应该建立在充分的研究分析和理性判断的基础之上，切不可盲目跟风或冲动行事。愿每一位投资者都能在套利投资的道路上收获成功与成长。

**风险提示**：本文内容仅供教育和参考之用，不构成任何投资建议或推荐。投资者应根据自身的风险承受能力和投资目标，在专业人士指导下做出独立的投资决策，并自行承担相应的投资风险。
        ''',
        '元数据': {
            '作者': '金融投资专家',
            '来源': '专业投资教育',
            '标签': ['投资套利', '风险管理', '金融策略', '投资理论', '市场分析'],
            '分类': '投资理财',
            '字数': len('投资套利策略全面解析：掌握低风险高收益的投资艺术'),
            '预计阅读时间': '8-10分钟'
        }
    }

def 交互式发布():
    """交互式发布文章"""
    print("\n🚀 启动交互式发布模式")
    print("=" * 50)
    
    try:
        from 微信自动发布器 import 微信自动发布器
        
        发布器 = 微信自动发布器()
        
        while True:
            print("\n📝 请选择操作:")
            print("1. 发布示例文章（推荐）")
            print("2. 发布自定义文章")
            print("3. 测试AI配图功能")
            print("4. 查看系统状态")
            print("0. 退出系统")
            
            选择 = input("\n请输入选项 (0-4): ").strip()
            
            if 选择 == '0':
                print("👋 感谢使用AI配图自动发布系统！")
                break
            elif 选择 == '1':
                发布示例文章(发布器)
            elif 选择 == '2':
                发布自定义文章(发布器)
            elif 选择 == '3':
                测试AI配图功能()
            elif 选择 == '4':
                查看系统状态(发布器)
            else:
                print("❌ 无效选项，请重新选择")
                
    except KeyboardInterrupt:
        print("\n\n👋 用户中断，系统退出")
    except Exception as e:
        print(f"❌ 系统运行异常: {str(e)}")

def 发布示例文章(发布器):
    """发布示例文章"""
    print("\n📝 准备发布示例文章...")
    
    文章数据 = 创建示例文章()
    
    # 发布选项
    发布选项 = {
        '仅草稿': True,  # 默认只创建草稿
        '排版样式': 'tech',
        '启用AI配图': True,
        'AI配图服务': 'smart_free',  # 使用Pollinations优先的智能配图
        '跳过确认': False,  # 需要用户确认
        '显示封面': True
    }
    
    print(f"📄 文章标题: {文章数据['标题']}")
    print(f"🎨 配图服务: {发布选项['AI配图服务']} (Pollinations优先)")
    print(f"📋 发布模式: {'仅草稿' if 发布选项['仅草稿'] else '直接发布'}")
    
    确认 = input("\n是否继续发布？(y/N): ").strip().lower()
    if 确认 not in ['y', 'yes', '是']:
        print("❌ 用户取消发布")
        return
    
    print("\n⏳ 开始发布...")
    开始时间 = datetime.now()
    
    结果 = 发布器.发布文章(文章数据, 发布选项)
    
    结束时间 = datetime.now()
    总耗时 = (结束时间 - 开始时间).total_seconds()
    
    if 结果['success']:
        print(f"\n🎉 发布成功!")
        print(f"📄 草稿ID: {结果['media_id']}")
        print(f"⏱️  总耗时: {总耗时:.2f}秒")
        print(f"\n💡 请登录微信公众号后台查看草稿箱验证效果")
    else:
        print(f"❌ 发布失败: {结果['error_message']}")

def 发布自定义文章(发布器):
    """发布自定义文章"""
    print("\n📝 自定义文章发布")
    print("请输入文章信息（输入空行结束）:")
    
    标题 = input("文章标题: ").strip()
    if not 标题:
        print("❌ 标题不能为空")
        return
    
    print("文章内容（输入'END'结束）:")
    内容行 = []
    while True:
        行 = input()
        if 行.strip() == 'END':
            break
        内容行.append(行)
    
    内容 = '\n'.join(内容行)
    if not 内容.strip():
        print("❌ 内容不能为空")
        return
    
    文章数据 = {
        '标题': 标题,
        '内容': 内容,
        '元数据': {
            '作者': '用户自定义',
            '来源': '手动输入',
            '标签': ['自定义'],
            '分类': '用户内容'
        }
    }
    
    # 发布选项
    发布选项 = {
        '仅草稿': True,
        '排版样式': 'tech',
        '启用AI配图': True,
        'AI配图服务': 'smart_free',
        '跳过确认': True,
        '显示封面': True
    }
    
    print(f"\n⏳ 开始发布文章: {标题}")
    结果 = 发布器.发布文章(文章数据, 发布选项)
    
    if 结果['success']:
        print(f"🎉 发布成功! 草稿ID: {结果['media_id']}")
    else:
        print(f"❌ 发布失败: {结果['error_message']}")

def 测试AI配图功能():
    """测试AI配图功能"""
    print("\n🎨 测试AI配图功能")
    print("=" * 40)
    
    try:
        from AI配图系统 import AI配图生成器
        
        配图生成器 = AI配图生成器()
        
        关键词 = input("请输入配图关键词: ").strip()
        if not 关键词:
            print("❌ 关键词不能为空")
            return
        
        print(f"\n🔍 正在为关键词 '{关键词}' 生成配图...")
        
        图片路径 = 配图生成器._智能免费配图(关键词, width=800, height=600)
        
        if 图片路径 and os.path.exists(图片路径):
            文件名 = os.path.basename(图片路径)
            文件大小 = os.path.getsize(图片路径) / 1024
            
            print(f"✅ 配图生成成功!")
            print(f"📁 文件名: {文件名}")
            print(f"📊 文件大小: {文件大小:.1f} KB")
            print(f"📂 保存路径: {图片路径}")
            
            # 判断使用的服务
            if 'pollinations_' in 文件名:
                print(f"🌸 使用服务: Pollinations AI (最佳效果)")
            elif 'huggingface_' in 文件名:
                print(f"🤗 使用服务: HuggingFace AI (很好效果)")
            elif 'themed_picsum_' in 文件名:
                print(f"🎯 使用服务: 主题化Picsum (良好效果)")
            elif 'picsum_' in 文件名:
                print(f"🎲 使用服务: 随机Picsum (保底效果)")
            else:
                print(f"💻 使用服务: 本地生成 (最终保底)")
        else:
            print("❌ 配图生成失败")
            
    except Exception as e:
        print(f"❌ 配图测试失败: {str(e)}")

def 查看系统状态(发布器):
    """查看系统状态"""
    print("\n📊 系统状态检查")
    print("=" * 40)
    
    try:
        # 检查配置
        配置有效, 错误列表 = 发布器.验证系统配置()
        
        if 配置有效:
            print("✅ 微信公众号配置: 正常")
        else:
            print("❌ 微信公众号配置: 异常")
            for error in 错误列表:
                print(f"   - {error}")
        
        # 检查目录
        自动发布器目录 = os.path.join(os.path.dirname(__file__), '源代码', '自动发布器')
        if os.path.exists(自动发布器目录):
            print("✅ 自动发布器目录: 存在")
        else:
            print("❌ 自动发布器目录: 不存在")
        
        # 检查图片目录
        图片目录1 = os.path.join(自动发布器目录, 'downloaded_images')
        图片目录2 = os.path.join(自动发布器目录, 'generated_images')
        
        if os.path.exists(图片目录1):
            图片数量1 = len([f for f in os.listdir(图片目录1) if f.endswith(('.jpg', '.png'))])
            print(f"✅ 下载图片目录: {图片数量1} 张图片")
        else:
            print("⚠️  下载图片目录: 不存在")
        
        if os.path.exists(图片目录2):
            图片数量2 = len([f for f in os.listdir(图片目录2) if f.endswith(('.jpg', '.png'))])
            print(f"✅ 生成图片目录: {图片数量2} 张图片")
        else:
            print("⚠️  生成图片目录: 不存在")
            
    except Exception as e:
        print(f"❌ 状态检查失败: {str(e)}")

def 直接发布投资文章():
    """直接发布投资套利文章到草稿"""
    print("🚀 开始发布投资套利文章到草稿...")

    try:
        from 微信自动发布器 import 微信自动发布器

        发布器 = 微信自动发布器()

        # 创建投资套利文章
        文章数据 = 创建示例文章()

        # 发布选项
        发布选项 = {
            '仅草稿': True,  # 只创建草稿
            '排版样式': 'business',  # 使用商务风格排版
            '启用AI配图': True,
            'AI配图服务': 'smart_free',  # 使用智能免费配图
            '跳过确认': True,  # 跳过用户确认
            '显示封面': True
        }

        print(f"📄 文章标题: {文章数据['标题']}")
        print(f"📝 文章字数: {len(文章数据['内容'])} 字")
        print(f"🎨 配图服务: {发布选项['AI配图服务']}")
        print(f"📋 发布模式: 仅草稿")
        print(f"🎯 排版样式: {发布选项['排版样式']}")

        print("\n⏳ 开始发布...")
        开始时间 = datetime.now()

        结果 = 发布器.发布文章(文章数据, 发布选项)

        结束时间 = datetime.now()
        总耗时 = (结束时间 - 开始时间).total_seconds()

        if 结果['success']:
            print(f"\n🎉 发布成功!")
            print(f"📄 草稿ID: {结果['media_id']}")
            print(f"⏱️  总耗时: {总耗时:.2f}秒")
            print(f"\n💡 请登录微信公众号后台查看草稿箱验证效果")
            print(f"🔍 检查要点:")
            print(f"   - 文字排版是否自动适配")
            print(f"   - AI配图是否成功生成")
            print(f"   - 整体版面是否美观")
        else:
            print(f"❌ 发布失败: {结果['error_message']}")

    except Exception as e:
        print(f"❌ 发布过程出错: {str(e)}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    try:
        print("🚀 微信公众号自动发布系统 - 投资文章测试")
        print("=" * 60)
        print("📅 启动时间:", datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        print("=" * 60)

        if not 验证系统环境():
            print("\n❌ 系统环境验证失败，请检查配置后重试")
            return

        print("\n✅ 系统环境验证通过，开始发布...")

        # 直接发布投资文章
        直接发布投资文章()

    except Exception as e:
        print(f"\n❌ 系统启动失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
